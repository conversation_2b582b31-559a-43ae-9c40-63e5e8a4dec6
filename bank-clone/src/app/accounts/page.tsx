import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { CreditCard, Shield, Clock, Users } from 'lucide-react';

export default function AccountsPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Account Management</h1>
          <p className="text-lg text-gray-600">
            Manage your banking accounts with ease and security
          </p>
        </div>

        {/* Account Types Section */}
        <div className="bg-white rounded-2xl shadow-lg p-8 mb-12">
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">Available Account Types</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="p-6 border border-gray-200 rounded-xl">
              <div className="flex items-center mb-4">
                <div className="p-3 bg-blue-100 rounded-lg">
                  <CreditCard className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 ml-3">Checking Account</h3>
              </div>
              <p className="text-gray-600">
                Perfect for daily transactions, bill payments, and managing your everyday finances with easy access to your funds.
              </p>
            </div>

            <div className="p-6 border border-gray-200 rounded-xl">
              <div className="flex items-center mb-4">
                <div className="p-3 bg-green-100 rounded-lg">
                  <CreditCard className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 ml-3">Savings Account</h3>
              </div>
              <p className="text-gray-600">
                Build your savings with competitive interest rates and secure your financial future with our savings solutions.
              </p>
            </div>

            <div className="p-6 border border-gray-200 rounded-xl">
              <div className="flex items-center mb-4">
                <div className="p-3 bg-purple-100 rounded-lg">
                  <CreditCard className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 ml-3">Credit Account</h3>
              </div>
              <p className="text-gray-600">
                Access credit when you need it with flexible repayment options and competitive rates for your financial needs.
              </p>
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          <div className="bg-white rounded-xl p-8 shadow-lg">
            <div className="flex items-center mb-4">
              <div className="p-3 bg-blue-100 rounded-lg">
                <Shield className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 ml-3">Secure Banking</h3>
            </div>
            <p className="text-gray-600 mb-4">
              Your accounts are protected with industry-leading security measures including multi-factor authentication,
              encryption, and 24/7 fraud monitoring.
            </p>
            <ul className="text-gray-600 space-y-2">
              <li>• Advanced encryption technology</li>
              <li>• Real-time fraud detection</li>
              <li>• Secure mobile and web access</li>
              <li>• FDIC insured deposits</li>
            </ul>
          </div>

          <div className="bg-white rounded-xl p-8 shadow-lg">
            <div className="flex items-center mb-4">
              <div className="p-3 bg-green-100 rounded-lg">
                <Clock className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 ml-3">24/7 Access</h3>
            </div>
            <p className="text-gray-600 mb-4">
              Access your accounts anytime, anywhere with our digital banking platform.
              Manage your finances on your schedule with round-the-clock availability.
            </p>
            <ul className="text-gray-600 space-y-2">
              <li>• Online banking portal</li>
              <li>• Mobile banking app</li>
              <li>• ATM network access</li>
              <li>• Customer support hotline</li>
            </ul>
          </div>
        </div>

        {/* Services Section */}
        <div className="bg-white rounded-2xl shadow-lg p-8">
          <div className="flex items-center mb-6">
            <div className="p-3 bg-purple-100 rounded-lg">
              <Users className="h-6 w-6 text-purple-600" />
            </div>
            <h2 className="text-2xl font-semibold text-gray-900 ml-3">Account Services</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Digital Services</h3>
              <ul className="text-gray-600 space-y-2">
                <li>• Online account opening</li>
                <li>• Digital statements and documents</li>
                <li>• Mobile check deposit</li>
                <li>• Bill pay and transfers</li>
                <li>• Account alerts and notifications</li>
                <li>• Budgeting and financial tools</li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Customer Support</h3>
              <ul className="text-gray-600 space-y-2">
                <li>• Dedicated relationship managers</li>
                <li>• 24/7 customer service</li>
                <li>• Branch and ATM locations</li>
                <li>• Financial planning assistance</li>
                <li>• Account maintenance support</li>
                <li>• Fraud protection services</li>
              </ul>
            </div>
          </div>

          <div className="mt-8 text-center">
            <p className="text-gray-600 mb-4">
              Ready to get started? Contact us to open your account today.
            </p>
            <button className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors">
              Contact Us
            </button>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
