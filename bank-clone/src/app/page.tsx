import Header from '@/components/Header';
import Hero from '@/components/Hero';
import ActionButtons from '@/components/ActionButtons';
import Footer from '@/components/Footer';

export default function Home() {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      <Hero />
      <ActionButtons />

      {/* Additional sections similar to the original site */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Discover Great Promotions
            </h2>
            <p className="text-lg text-gray-600">
              Stay up-to-date with our latest highlights
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 border border-blue-200">
              <div className="h-40 bg-blue-200 rounded-xl mb-4"></div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Special Offers</h3>
              <p className="text-gray-600 text-sm">Exclusive deals for our valued customers</p>
            </div>

            <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-6 border border-green-200">
              <div className="h-40 bg-green-200 rounded-xl mb-4"></div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">New Products</h3>
              <p className="text-gray-600 text-sm">Latest financial products and services</p>
            </div>

            <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-6 border border-purple-200">
              <div className="h-40 bg-purple-200 rounded-xl mb-4"></div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Announcements</h3>
              <p className="text-gray-600 text-sm">Important updates and news</p>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
