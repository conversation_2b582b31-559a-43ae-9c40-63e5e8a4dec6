import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    if (!email) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 });
    }

    // Read existing user data
    const dataPath = path.join(process.cwd(), 'src/data/users.json');
    const userData = JSON.parse(fs.readFileSync(dataPath, 'utf8'));

    // Check if email exists in users database
    const userExists = userData.users.find((user: { email: string }) => user.email === email);

    if (!userExists) {
      return NextResponse.json({
        error: 'User not found. Please contact support.'
      }, { status: 400 });
    }

    // Use universal OTP
    const otp = '981256';

    // Create OTP session
    const otpSession = {
      email,
      otp,
      timestamp: new Date().toISOString(),
      expires: new Date(Date.now() + 10 * 60 * 1000).toISOString(), // 10 minutes
      verified: false
    };

    // Remove any existing OTP sessions for this email
    userData.otpSessions = userData.otpSessions.filter((session: { email: string }) => session.email !== email);

    // Add new OTP session
    userData.otpSessions.push(otpSession);

    // Save updated data
    fs.writeFileSync(dataPath, JSON.stringify(userData, null, 2));

    // In a real application, you would send the OTP via email service
    console.log(`OTP sent to ${email}: ${otp}`);

    return NextResponse.json({
      success: true,
      message: 'OTP sent successfully to your email address'
    });

  } catch (error) {
    console.error('Error sending OTP:', error);
    return NextResponse.json({ error: 'Failed to send OTP' }, { status: 500 });
  }
}
