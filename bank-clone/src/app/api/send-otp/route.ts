import { NextRequest, NextResponse } from 'next/server';
import { STATIC_USERS, STATIC_OTP } from '@/utils/constants';

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    if (!email) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 });
    }

    // Check if email exists in static users database
    const userExists = STATIC_USERS.find((user) => user.email === email);

    if (!userExists) {
      return NextResponse.json({
        error: 'User not found. Please contact support.'
      }, { status: 400 });
    }

    // Use universal OTP (static for demo)
    const otp = STATIC_OTP;

    // In a real application, you would send the OTP via email service
    console.log(`OTP sent to ${email}: ${otp}`);

    return NextResponse.json({
      success: true,
      message: 'OTP sent successfully to your email address'
    });

  } catch (error) {
    console.error('Error sending OTP:', error);
    return NextResponse.json({ error: 'Failed to send OTP' }, { status: 500 });
  }
}
