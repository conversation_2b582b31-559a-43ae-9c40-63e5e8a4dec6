import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function POST(request: NextRequest) {
  try {
    const { email, otp } = await request.json();

    if (!email || !otp) {
      return NextResponse.json({ error: '<PERSON>ail and O<PERSON> are required' }, { status: 400 });
    }

    // Read existing user data
    const dataPath = path.join(process.cwd(), 'src/data/users.json');
    const userData = JSON.parse(fs.readFileSync(dataPath, 'utf8'));

    // Find the OTP session
    const otpSession = userData.otpSessions.find((session: { email: string; verified: boolean }) =>
      session.email === email && !session.verified
    );

    if (!otpSession) {
      return NextResponse.json({ error: 'No valid OTP session found' }, { status: 400 });
    }

    // Check if O<PERSON> has expired
    if (new Date() > new Date(otpSession.expires)) {
      return NextResponse.json({ error: 'OTP has expired' }, { status: 400 });
    }

    // Verify OTP
    if (otpSession.otp !== otp) {
      return NextResponse.json({ error: 'Invalid OTP' }, { status: 400 });
    }

    // Mark OTP as verified
    otpSession.verified = true;
    otpSession.verifiedAt = new Date().toISOString();

    // Save updated data
    fs.writeFileSync(dataPath, JSON.stringify(userData, null, 2));

    return NextResponse.json({
      success: true,
      message: 'OTP verified successfully'
    });

  } catch (error) {
    console.error('Error verifying OTP:', error);
    return NextResponse.json({ error: 'Failed to verify OTP' }, { status: 500 });
  }
}
