import { NextRequest, NextResponse } from 'next/server';
import { STATIC_USERS, STATIC_OTP } from '@/utils/constants';

export async function POST(request: NextRequest) {
  try {
    const { email, otp } = await request.json();

    if (!email || !otp) {
      return NextResponse.json({ error: 'Email and OTP are required' }, { status: 400 });
    }

    // Check if email exists in static users database
    const userExists = STATIC_USERS.find((user) => user.email === email);

    if (!userExists) {
      return NextResponse.json({ error: 'User not found. Please contact support.' }, { status: 400 });
    }

    // Verify static OTP
    if (otp !== STATIC_OTP) {
      return NextResponse.json({ error: 'Invalid OTP' }, { status: 400 });
    }

    return NextResponse.json({
      success: true,
      message: 'OTP verified successfully'
    });

  } catch (error) {
    console.error('Error verifying OTP:', error);
    return NextResponse.json({ error: 'Failed to verify OTP' }, { status: 500 });
  }
}
