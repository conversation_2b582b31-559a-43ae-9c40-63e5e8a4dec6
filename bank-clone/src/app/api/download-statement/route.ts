import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import { STATIC_USERS } from '@/utils/constants';

export async function POST(request: NextRequest) {
  try {
    const { email, period } = await request.json();

    if (!email || !period) {
      return NextResponse.json({ error: 'Email and period are required' }, { status: 400 });
    }

    // Only "Last Month" period is allowed
    if (period !== 'Last Month') {
      return NextResponse.json({ error: 'Only Last Month statements are available' }, { status: 400 });
    }

    // Verify email exists in static users database
    const userExists = STATIC_USERS.find((user) => user.email === email);

    if (!userExists) {
      return NextResponse.json({ error: 'User not found. Please contact support.' }, { status: 400 });
    }

    // Read the existing PDF file
    const pdfPath = path.join(process.cwd(), 'src/data/statement.pdf');

    if (!fs.existsSync(pdfPath)) {
      return NextResponse.json({ error: 'Statement file not found' }, { status: 404 });
    }

    const pdfBuffer = fs.readFileSync(pdfPath);
    const base64Pdf = pdfBuffer.toString('base64');

    return NextResponse.json({
      success: true,
      pdfData: base64Pdf,
      filename: `statement-${period.toLowerCase().replace(/\s+/g, '-')}.pdf`
    });

  } catch (error) {
    console.error('Error serving statement:', error);
    return NextResponse.json({ error: 'Failed to serve statement' }, { status: 500 });
  }
}


