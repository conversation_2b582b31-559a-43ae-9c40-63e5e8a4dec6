import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function POST(request: NextRequest) {
  try {
    const { email, period } = await request.json();

    // Validate email and period (same validation as download endpoint)
    if (!email || !period) {
      return NextResponse.json({
        success: false,
        error: 'Email and period are required'
      }, { status: 400 });
    }

    // Only allow "Last Month" period
    if (period !== 'Last Month') {
      return NextResponse.json({
        success: false,
        error: 'Only Last Month statements are available'
      }, { status: 400 });
    }

    // Validate email against users database
    const usersPath = path.join(process.cwd(), 'src', 'data', 'users.json');
    const usersData = JSON.parse(fs.readFileSync(usersPath, 'utf8'));

    const user = usersData.users.find((u: { email: string }) => u.email === email);
    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Invalid email address'
      }, { status: 400 });
    }

    // Get the PDF file path
    const pdfPath = path.join(process.cwd(), 'src', 'data', 'statement.pdf');

    // Check if file exists
    if (!fs.existsSync(pdfPath)) {
      return NextResponse.json({
        success: false,
        error: 'Statement file not found'
      }, { status: 404 });
    }

    // Get file stats to determine size
    const stats = fs.statSync(pdfPath);
    const fileSizeInBytes = stats.size;

    // Convert bytes to human-readable format
    const formatFileSize = (bytes: number): string => {
      if (bytes === 0) return '0 Bytes';

      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));

      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    const formattedSize = formatFileSize(fileSizeInBytes);

    return NextResponse.json({
      success: true,
      fileSize: formattedSize,
      fileSizeBytes: fileSizeInBytes,
      filename: `statement_${period.toLowerCase().replace(' ', '_')}.pdf`
    });

  } catch (error) {
    console.error('Error getting file size:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
