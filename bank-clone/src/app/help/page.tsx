import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { MessageCircle, Phone, Mail, Clock, Search, ChevronRight } from 'lucide-react';

export default function HelpPage() {
  const faqItems = [
    {
      question: "How do I reset my password?",
      answer: "You can reset your password by clicking 'Forgot Password' on the login page and following the instructions sent to your email."
    },
    {
      question: "How do I download my statements?",
      answer: "Go to the Statements page, select your desired period, and follow the verification process to download your PDF statement."
    },
    {
      question: "What are the daily transaction limits?",
      answer: "Daily limits vary by account type. Check your account details or contact support for specific limits."
    },
    {
      question: "How do I report a suspicious transaction?",
      answer: "Contact our fraud department immediately at 1-800-FRAUD or use the secure message center in your account."
    }
  ];

  const contactMethods = [
    {
      icon: Phone,
      title: "Phone Support",
      description: "Speak with our customer service team",
      details: "1-800-AFFIN-BANK",
      availability: "24/7 Available",
      color: "blue"
    },
    {
      icon: MessageCircle,
      title: "Live Chat",
      description: "Get instant help through chat",
      details: "Start Chat",
      availability: "Mon-Fri 8AM-8PM",
      color: "green"
    },
    {
      icon: Mail,
      title: "Email Support",
      description: "Send us a detailed message",
      details: "<EMAIL>",
      availability: "Response within 24hrs",
      color: "purple"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">How Can We Help?</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Find answers to your questions or get in touch with our support team
          </p>
        </div>

        {/* Search Bar */}
        <div className="max-w-2xl mx-auto mb-12">
          <div className="relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search for help topics..."
              className="w-full pl-12 pr-4 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg"
            />
          </div>
        </div>

        {/* Contact Methods */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16">
          {contactMethods.map((method, index) => (
            <div key={index} className="bg-white rounded-2xl shadow-lg p-6 hover:shadow-xl transition-shadow">
              <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg mb-4 ${
                method.color === 'blue' ? 'bg-blue-100' :
                method.color === 'green' ? 'bg-green-100' : 'bg-purple-100'
              }`}>
                <method.icon className={`h-6 w-6 ${
                  method.color === 'blue' ? 'text-blue-600' :
                  method.color === 'green' ? 'text-green-600' : 'text-purple-600'
                }`} />
              </div>
              
              <h3 className="text-xl font-semibold text-gray-900 mb-2">{method.title}</h3>
              <p className="text-gray-600 mb-4">{method.description}</p>
              
              <div className="space-y-2">
                <p className="font-medium text-gray-900">{method.details}</p>
                <div className="flex items-center text-sm text-gray-500">
                  <Clock className="h-4 w-4 mr-1" />
                  {method.availability}
                </div>
              </div>
              
              <button className={`w-full mt-4 px-4 py-2 rounded-lg font-medium transition-colors ${
                method.color === 'blue' ? 'bg-blue-600 hover:bg-blue-700 text-white' :
                method.color === 'green' ? 'bg-green-600 hover:bg-green-700 text-white' :
                'bg-purple-600 hover:bg-purple-700 text-white'
              }`}>
                Contact Now
              </button>
            </div>
          ))}
        </div>

        {/* Quick Help Categories */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Quick Help Categories</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow cursor-pointer group">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-blue-200 transition-colors">
                  <span className="text-2xl">🏦</span>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Account Management</h3>
                <p className="text-sm text-gray-600">Account settings, passwords, and profile updates</p>
              </div>
            </div>
            
            <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow cursor-pointer group">
              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-green-200 transition-colors">
                  <span className="text-2xl">💳</span>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Cards & Payments</h3>
                <p className="text-sm text-gray-600">Card issues, payments, and transaction problems</p>
              </div>
            </div>
            
            <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow cursor-pointer group">
              <div className="text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-purple-200 transition-colors">
                  <span className="text-2xl">🔒</span>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Security & Fraud</h3>
                <p className="text-sm text-gray-600">Report fraud, security concerns, and suspicious activity</p>
              </div>
            </div>
            
            <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow cursor-pointer group">
              <div className="text-center">
                <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-orange-200 transition-colors">
                  <span className="text-2xl">📱</span>
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Mobile App</h3>
                <p className="text-sm text-gray-600">App installation, login issues, and mobile features</p>
              </div>
            </div>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="bg-white rounded-2xl shadow-lg p-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Frequently Asked Questions</h2>
          
          <div className="space-y-4">
            {faqItems.map((item, index) => (
              <details key={index} className="group border border-gray-200 rounded-lg">
                <summary className="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50">
                  <span className="font-medium text-gray-900">{item.question}</span>
                  <ChevronRight className="h-5 w-5 text-gray-400 group-open:rotate-90 transition-transform" />
                </summary>
                <div className="px-4 pb-4">
                  <p className="text-gray-600">{item.answer}</p>
                </div>
              </details>
            ))}
          </div>
          
          <div className="mt-8 text-center">
            <button className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700">
              View All FAQs
            </button>
          </div>
        </div>

        {/* Emergency Contact */}
        <div className="mt-12 bg-red-50 border border-red-200 rounded-2xl p-6">
          <div className="text-center">
            <h3 className="text-xl font-bold text-red-900 mb-2">Emergency Banking Support</h3>
            <p className="text-red-700 mb-4">
              For urgent issues like lost cards, fraud, or account lockouts
            </p>
            <button className="bg-red-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-red-700">
              Call Emergency Line: 1-800-EMERGENCY
            </button>
          </div>
        </div>
      </div>
      
      <Footer />
    </div>
  );
}
