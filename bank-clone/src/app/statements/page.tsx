'use client';

import { useState } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Calendar, Download, Mail, Shield } from 'lucide-react';

export default function StatementsPage() {
  const [selectedPeriod, setSelectedPeriod] = useState<string | null>(null);
  const [email, setEmail] = useState('');
  const [otp, setOtp] = useState('');
  const [step, setStep] = useState<'select' | 'email' | 'otp' | 'download'>('select');
  const [isLoading, setIsLoading] = useState(false);
  const [fileSize, setFileSize] = useState<string>('Loading...');
  const [filename, setFilename] = useState<string>('');

  const handlePeriodSelect = (period: string) => {
    setSelectedPeriod(period);
    if (period === 'Last Month') {
      setStep('email');
    }
    // Only Last Month option is available - others are disabled
  };

  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await fetch('/api/send-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (data.success) {
        setStep('otp');
      } else {
        alert(data.error || 'Failed to send OTP');
      }
    } catch {
      alert('Failed to send OTP. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleOtpSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await fetch('/api/verify-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, otp }),
      });

      const data = await response.json();

      if (data.success) {
        setStep('download');
        // Fetch file size when moving to download step
        fetchFileSize();
      } else {
        alert(data.error || 'Invalid OTP');
      }
    } catch {
      alert('Failed to verify OTP. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchFileSize = async () => {
    try {
      const response = await fetch('/api/get-file-size', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, period: selectedPeriod }),
      });

      const data = await response.json();

      if (data.success) {
        setFileSize(data.fileSize);
        setFilename(data.filename);
      } else {
        setFileSize('Unknown');
      }
    } catch {
      setFileSize('Unknown');
    }
  };

  const handleDownload = async () => {
    try {
      const response = await fetch('/api/download-statement', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, period: selectedPeriod }),
      });

      const data = await response.json();

      if (data.success) {
        // Convert base64 to blob and download
        const byteCharacters = atob(data.pdfData);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: 'application/pdf' });

        // Create download link
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = data.filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      } else {
        alert(data.error || 'Failed to download statement');
      }
    } catch {
      alert('Failed to download statement. Please try again.');
    }
  };

  const resetFlow = () => {
    setSelectedPeriod(null);
    setEmail('');
    setOtp('');
    setStep('select');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Statements</h1>
          <p className="text-lg text-gray-600">
            Download your monthly account statements with secure verification
          </p>
        </div>

        <div className="bg-white rounded-2xl shadow-lg p-8">
          {step === 'select' && (
            <div>
              <h2 className="text-2xl font-semibold text-gray-900 mb-6">
                Select Statement Period
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="group p-6 border-2 border-gray-200 rounded-xl">
                  <Calendar className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Last 7 Days</h3>
                  <p className="text-gray-600 text-sm">Recent transactions and activities</p>
                </div>

                <button
                  onClick={() => handlePeriodSelect('Last Month')}
                  className="group p-6 border-2 border-gray-200 rounded-xl hover:border-green-500 hover:bg-green-50 transition-all"
                >
                  <Mail className="h-12 w-12 text-green-600 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Last Month</h3>
                  <p className="text-gray-600 text-sm">Complete monthly statement with verification</p>
                </button>

                <div className="group p-6 border-2 border-gray-200 rounded-xl">
                  <Calendar className="h-12 w-12 text-purple-600 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Custom Date</h3>
                  <p className="text-gray-600 text-sm">Choose your own date range</p>
                </div>
              </div>
            </div>
          )}

          {step === 'email' && (
            <div>
              <div className="text-center mb-6">
                <Shield className="h-16 w-16 text-blue-600 mx-auto mb-4" />
                <h2 className="text-2xl font-semibold text-gray-900 mb-2">
                  Email Verification Required
                </h2>
                <p className="text-gray-600">
                  For security purposes, please enter your email to receive an OTP
                </p>
              </div>

              <form onSubmit={handleEmailSubmit} className="max-w-md mx-auto">
                <div className="mb-6">
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter your email address"
                    required
                  />
                </div>

                <div className="flex gap-4">
                  <button
                    type="button"
                    onClick={resetFlow}
                    className="flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                  >
                    Back
                  </button>
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="flex-1 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                  >
                    {isLoading ? 'Sending...' : 'Send OTP'}
                  </button>
                </div>
              </form>
            </div>
          )}

          {step === 'otp' && (
            <div>
              <div className="text-center mb-6">
                <Mail className="h-16 w-16 text-green-600 mx-auto mb-4" />
                <h2 className="text-2xl font-semibold text-gray-900 mb-2">
                  Enter OTP
                </h2>
                <p className="text-gray-600">
                  We&apos;ve sent a 6-digit code to {email}
                </p>
              </div>

              <form onSubmit={handleOtpSubmit} className="max-w-md mx-auto">
                <div className="mb-6">
                  <label htmlFor="otp" className="block text-sm font-medium text-gray-700 mb-2">
                    OTP Code
                  </label>
                  <input
                    type="text"
                    id="otp"
                    value={otp}
                    onChange={(e) => setOtp(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-center text-2xl tracking-widest"
                    placeholder="000000"
                    maxLength={6}
                    required
                  />
                </div>

                <div className="flex gap-4">
                  <button
                    type="button"
                    onClick={() => setStep('email')}
                    className="flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                  >
                    Back
                  </button>
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="flex-1 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                  >
                    {isLoading ? 'Verifying...' : 'Verify OTP'}
                  </button>
                </div>
              </form>
            </div>
          )}

          {step === 'download' && (
            <div>
              <div className="text-center mb-6">
                <Download className="h-16 w-16 text-green-600 mx-auto mb-4" />
                <h2 className="text-2xl font-semibold text-gray-900 mb-2">
                  Download Ready
                </h2>
                <p className="text-gray-600">
                  Your {selectedPeriod} statement is ready for download
                </p>
              </div>

              <div className="max-w-md mx-auto">
                <div className="bg-gray-50 rounded-lg p-4 mb-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-semibold text-gray-900">Statement Details</h3>
                      <p className="text-sm text-gray-600">Period: {selectedPeriod}</p>
                      <p className="text-sm text-gray-600">Format: PDF</p>
                      {filename && (
                        <p className="text-sm text-gray-600">File: {filename}</p>
                      )}
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-600">File Size</p>
                      <p className="font-semibold">{fileSize}</p>
                    </div>
                  </div>
                </div>

                <div className="flex gap-4">
                  <button
                    onClick={resetFlow}
                    className="flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                  >
                    New Request
                  </button>
                  <button
                    onClick={handleDownload}
                    className="flex-1 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center justify-center gap-2"
                  >
                    <Download className="h-5 w-5" />
                    Download PDF
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      <Footer />
    </div>
  );
}
