// Static user data for Vercel deployment (read-only)
// This replaces the need for file system writes in serverless functions
export const STATIC_USERS = [
  {
    "id": 1,
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "accountNumber": "****1234"
  },
  {
    "id": 2,
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "accountNumber": "****5678"
  },
  {
    "id": 3,
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "accountNumber": "****9012"
  },
  {
    "id": 4,
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "accountNumber": "****3456"
  },
  {
    "id": 5,
    "name": "Test User",
    "email": "<EMAIL>",
    "accountNumber": "****7890"
  }
];

// Static OTP for demo purposes (as requested by user)
export const STATIC_OTP = '981256';

// User type definition
export interface User {
  id: number;
  name: string;
  email: string;
  accountNumber: string;
}
