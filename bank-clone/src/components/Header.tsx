'use client';

import Link from 'next/link';
import Image from 'next/image';
import { User } from 'lucide-react';

export default function Header() {

  return (
    <header className="bg-black shadow-sm border-b border-gray-800">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center h-16 px-2">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center">
              <div className="w-60 h-28 relative">
                <Image
                  src="/images/logo.jpeg"
                  alt="Bank Logo"
                  fill
                  className="object-contain"
                />
              </div>
            </Link>
          </div>

          {/* Right side content */}
          <div className="flex items-center space-x-4">
            <span className="text-gray-300 text-sm hidden md:block">Digital Banking</span>
            <button className="p-2 text-gray-300 hover:text-white transition-colors">
              <User className="h-6 w-6" />
            </button>
          </div>
        </div>
      </div>
    </header>
  );
}
