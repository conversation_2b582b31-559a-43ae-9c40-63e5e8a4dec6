# Environment Variables for Affin Banking App

# Node Environment
NODE_ENV=production

# Next.js Configuration
NEXT_PUBLIC_APP_URL=https://your-app-domain.vercel.app

# Application Settings
NEXT_PUBLIC_APP_NAME="AffinAlways Banking"
NEXT_PUBLIC_APP_VERSION="1.0.0"

# Security Settings (Optional - for future use)
# JWT_SECRET=your-jwt-secret-here
# ENCRYPTION_KEY=your-encryption-key-here

# Database Settings (Optional - for future use)
# DATABASE_URL=your-database-url-here

# Email Settings (Optional - for future use)
# SMTP_HOST=your-smtp-host
# SMTP_PORT=587
# SMTP_USER=your-smtp-user
# SMTP_PASS=your-smtp-password
